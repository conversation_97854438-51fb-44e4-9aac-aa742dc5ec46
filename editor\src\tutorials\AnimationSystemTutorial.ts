/**
 * 动画系统教程
 * 引导用户学习如何创建和编辑动画
 */
import { Tutorial, TutorialStep } from '../services/TutorialService';
import i18n from '../i18n';

/**
 * 创建动画系统教程
 * 使用函数来确保i18n已经初始化
 */
export const createAnimationSystemTutorial = (): Tutorial => {
  // 确保i18n已经初始化
  if (!i18n.isInitialized) {
    console.warn('i18n not initialized, using fallback values');
  }

  return {
  id: 'animation-system',
  title: i18n.t('tutorials.animationSystem.title') as string,
  description: i18n.t('tutorials.animationSystem.description') as string,
  category: 'animation',
  difficulty: 'intermediate',
  duration: 25,
  prerequisites: ['editor-basics'],
  tags: ['动画', '骨骼', '状态机', '混合树'],
  steps: [
    // 步骤1：介绍
    {
      id: 'introduction',
      title: i18n.t('tutorials.animationSystem.steps.introduction.title') as string,
      description: i18n.t('tutorials.animationSystem.steps.introduction.description') as string,
      nextButtonText: i18n.t('tutorials.next') as string,
      skipButtonText: i18n.t('tutorials.skip') as string
    },
    
    // 步骤2：打开动画编辑器
    {
      id: 'open-animation-editor',
      title: i18n.t('tutorials.animationSystem.steps.openAnimationEditor.title') as string,
      description: i18n.t('tutorials.animationSystem.steps.openAnimationEditor.description') as string,
      targetElement: 'menu-window',
      highlightElement: true,
      action: 'click',
      completionCriteria: 'animation-editor-opened',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤3：导入动画模型
    {
      id: 'import-animation-model',
      title: i18n.t('tutorials.animationSystem.steps.importAnimationModel.title') as string,
      description: i18n.t('tutorials.animationSystem.steps.importAnimationModel.description') as string,
      targetElement: 'import-button',
      highlightElement: true,
      action: 'click',
      completionCriteria: 'model-imported',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤4：检查骨骼结构
    {
      id: 'check-skeleton',
      title: i18n.t('tutorials.animationSystem.steps.checkSkeleton.title') as string,
      description: i18n.t('tutorials.animationSystem.steps.checkSkeleton.description') as string,
      targetElement: 'skeleton-view',
      highlightElement: true,
      action: 'inspect',
      completionCriteria: 'skeleton-checked',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },
    
    // 步骤5：创建动画片段
    {
      id: 'create-animation-clip',
      title: i18n.t('tutorials.animationSystem.steps.createAnimationClip.title') as string,
      description: i18n.t('tutorials.animationSystem.steps.createAnimationClip.description') as string,
      targetElement: 'create-clip-button',
      highlightElement: true,
      action: 'click',
      completionCriteria: 'clip-created',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤6：设置关键帧
    {
      id: 'set-keyframes',
      title: i18n.t('tutorials.animationSystem.steps.setKeyframes.title') as string,
      description: i18n.t('tutorials.animationSystem.steps.setKeyframes.description') as string,
      targetElement: 'timeline',
      highlightElement: true,
      action: 'click-drag',
      completionCriteria: 'keyframes-set',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤7：编辑曲线
    {
      id: 'edit-curves',
      title: i18n.t('tutorials.animationSystem.steps.editCurves.title') as string,
      description: i18n.t('tutorials.animationSystem.steps.editCurves.description') as string,
      targetElement: 'curve-editor',
      highlightElement: true,
      action: 'click-drag',
      completionCriteria: 'curves-edited',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤8：预览动画
    {
      id: 'preview-animation',
      title: i18n.t('tutorials.animationSystem.steps.previewAnimation.title') as string,
      description: i18n.t('tutorials.animationSystem.steps.previewAnimation.description') as string,
      targetElement: 'preview-button',
      highlightElement: true,
      action: 'click',
      completionCriteria: 'animation-previewed',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },
    
    // 步骤9：创建动画状态机
    {
      id: 'create-state-machine',
      title: i18n.t('tutorials.animationSystem.steps.createStateMachine.title') as string,
      description: i18n.t('tutorials.animationSystem.steps.createStateMachine.description') as string,
      targetElement: 'state-machine-tab',
      highlightElement: true,
      action: 'click',
      completionCriteria: 'state-machine-created',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤10：添加状态
    {
      id: 'add-states',
      title: i18n.t('tutorials.animationSystem.steps.addStates.title') as string,
      description: i18n.t('tutorials.animationSystem.steps.addStates.description') as string,
      targetElement: 'add-state-button',
      highlightElement: true,
      action: 'click',
      completionCriteria: 'states-added',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤11：创建转换
    {
      id: 'create-transitions',
      title: i18n.t('tutorials.animationSystem.steps.createTransitions.title') as string,
      description: i18n.t('tutorials.animationSystem.steps.createTransitions.description') as string,
      targetElement: 'state-machine-canvas',
      highlightElement: true,
      action: 'drag',
      completionCriteria: 'transitions-created',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤12：设置转换条件
    {
      id: 'set-transition-conditions',
      title: i18n.t('tutorials.animationSystem.steps.setTransitionConditions.title') as string,
      description: i18n.t('tutorials.animationSystem.steps.setTransitionConditions.description') as string,
      targetElement: 'transition-inspector',
      highlightElement: true,
      action: 'edit',
      completionCriteria: 'conditions-set',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },
    
    // 步骤13：创建混合树
    {
      id: 'create-blend-tree',
      title: i18n.t('tutorials.animationSystem.steps.createBlendTree.title') as string,
      description: i18n.t('tutorials.animationSystem.steps.createBlendTree.description') as string,
      targetElement: 'blend-tree-button',
      highlightElement: true,
      action: 'click',
      completionCriteria: 'blend-tree-created',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤14：添加混合参数
    {
      id: 'add-blend-parameters',
      title: i18n.t('tutorials.animationSystem.steps.addBlendParameters.title') as string,
      description: i18n.t('tutorials.animationSystem.steps.addBlendParameters.description') as string,
      targetElement: 'parameters-panel',
      highlightElement: true,
      action: 'edit',
      completionCriteria: 'parameters-added',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤15：测试动画系统
    {
      id: 'test-animation-system',
      title: i18n.t('tutorials.animationSystem.steps.testAnimationSystem.title') as string,
      description: i18n.t('tutorials.animationSystem.steps.testAnimationSystem.description') as string,
      targetElement: 'test-button',
      highlightElement: true,
      action: 'click',
      completionCriteria: 'system-tested',
      nextButtonText: i18n.t('tutorials.next') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤16：保存动画资产
    {
      id: 'save-animation-assets',
      title: i18n.t('tutorials.animationSystem.steps.saveAnimationAssets.title') as string,
      description: i18n.t('tutorials.animationSystem.steps.saveAnimationAssets.description') as string,
      targetElement: 'save-button',
      highlightElement: true,
      action: 'click',
      completionCriteria: 'assets-saved',
      nextButtonText: i18n.t('tutorials.finish') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    },

    // 步骤17：完成
    {
      id: 'completion',
      title: i18n.t('tutorials.animationSystem.steps.completion.title') as string,
      description: i18n.t('tutorials.animationSystem.steps.completion.description') as string,
      nextButtonText: i18n.t('tutorials.close') as string,
      previousButtonText: i18n.t('tutorials.previous') as string
    }
  ]
  };
};

// 导出教程实例
export const AnimationSystemTutorial = createAnimationSystemTutorial();

export default AnimationSystemTutorial;
